import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Platform, Text } from 'react-native';
import { NativeAd, NativeAdView, HeadlineView, TaglineView, AdvertiserView, CallToActionView, IconView, ImageView, TestIds } from 'react-native-google-mobile-ads';
import { Colors, Spacing, Shadow } from '../theme';

interface AdMobNativeProps {
  style?: any;
  onAdLoaded?: () => void;
  onAdFailedToLoad?: (error: any) => void;
}

const AdMobNative: React.FC<AdMobNativeProps> = ({
  style,
  onAdLoaded,
  onAdFailedToLoad,
}) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  // Production native ad unit ID
  const adUnitId = Platform.select({
    ios: 'ca-app-pub-8809398979690427/4326679585', // Your iOS native ad unit
    android: 'ca-app-pub-8809398979690427/4326679585', // Your Android native ad unit (same for now)
  }) || TestIds.NATIVE;

  // Debug logging
  useEffect(() => {
    console.log('🎯 AdMob Native: Component mounted');
    console.log('🎯 AdMob Native: Using ad unit ID:', adUnitId);
    console.log('🎯 AdMob Native: Platform:', Platform.OS);
  }, [adUnitId]);

  const handleAdLoaded = () => {
    console.log('✅ AdMob Native: Ad loaded successfully');
    setIsLoaded(true);
    setHasError(false);
    onAdLoaded?.();
  };

  const handleAdFailedToLoad = (error: any) => {
    console.log('❌ AdMob Native: Failed to load ad:', error);
    setIsLoaded(false);
    setHasError(true);
    onAdFailedToLoad?.(error);
  };

  // Show debug info if there's an error
  if (hasError) {
    return (
      <View style={[styles.container, styles.errorContainer, style]}>
        <Text style={styles.debugText}>AdMob Native: Failed to load</Text>
        <Text style={styles.debugText}>Ad Unit: {adUnitId}</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, style]}>
      <NativeAd
        unitId={adUnitId}
        requestOptions={{
          requestNonPersonalizedAdsOnly: false,
          keywords: ['health', 'food', 'cosmetics', 'wellness', 'nutrition'],
        }}
        onAdLoaded={handleAdLoaded}
        onAdFailedToLoad={handleAdFailedToLoad}
      >
        <NativeAdView style={styles.nativeAdView}>
          <View style={styles.adHeader}>
            <IconView style={styles.adIcon} />
            <View style={styles.adHeaderText}>
              <HeadlineView style={styles.adHeadline} />
              <AdvertiserView style={styles.adAdvertiser} />
            </View>
          </View>
          
          <ImageView style={styles.adImage} />
          
          <View style={styles.adContent}>
            <TaglineView style={styles.adTagline} />
            <CallToActionView style={styles.adCallToAction} />
          </View>
        </NativeAdView>
      </NativeAd>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.BackgroundPrimary,
    borderRadius: 16,
    overflow: 'hidden',
    ...Shadow.Medium,
  },
  errorContainer: {
    backgroundColor: Colors.Error,
    padding: Spacing.Medium,
    minHeight: 120,
  },
  debugText: {
    color: Colors.BackgroundPrimary,
    fontSize: 12,
    textAlign: 'center',
    marginBottom: Spacing.Small,
  },
  nativeAdView: {
    width: '100%',
    padding: Spacing.Medium,
  },
  adHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: Spacing.Medium,
  },
  adIcon: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: Spacing.Medium,
  },
  adHeaderText: {
    flex: 1,
  },
  adHeadline: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.DarkText,
    marginBottom: 2,
  },
  adAdvertiser: {
    fontSize: 12,
    color: Colors.LightText,
  },
  adImage: {
    width: '100%',
    height: 120,
    borderRadius: 12,
    marginBottom: Spacing.Medium,
  },
  adContent: {
    alignItems: 'center',
  },
  adTagline: {
    fontSize: 14,
    color: Colors.DarkText,
    textAlign: 'center',
    marginBottom: Spacing.Medium,
    lineHeight: 20,
  },
  adCallToAction: {
    backgroundColor: Colors.Primary,
    paddingVertical: Spacing.Medium,
    paddingHorizontal: Spacing.Large,
    borderRadius: 8,
    minWidth: 120,
    alignItems: 'center',
  },
});

export default AdMobNative;
