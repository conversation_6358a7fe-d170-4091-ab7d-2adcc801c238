import mobileAds, { MaxAdContentRating } from 'react-native-google-mobile-ads';

class AdMobService {
  private static instance: AdMobService;
  private isInitialized = false;

  private constructor() {}

  public static getInstance(): AdMobService {
    if (!AdMobService.instance) {
      AdMobService.instance = new AdMobService();
    }
    return AdMobService.instance;
  }

  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      await mobileAds().initialize();
      
      // Configure ad settings for production
      await mobileAds().setRequestConfiguration({
        // Update all future requests suitable for parental guidance
        maxAdContentRating: MaxAdContentRating.PG,

        // Set to false for general audience (health/food app)
        tagForChildDirectedTreatment: false,

        // Set to false for general audience
        tagForUnderAgeOfConsent: false,

        // Test device IDs (only in development)
        testDeviceIdentifiers: __DEV__ ? ['EMULATOR'] : [],
      });

      this.isInitialized = true;
      console.log('✅ AdMob initialized successfully');
      console.log('🎯 AdMob Service: Ready to serve ads');
    } catch (error) {
      console.error('Failed to initialize AdMob:', error);
      throw error;
    }
  }

  public isAdMobInitialized(): boolean {
    return this.isInitialized;
  }
}

export default AdMobService;
