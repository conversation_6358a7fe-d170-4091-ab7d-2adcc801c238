CLANG_CXX_LANGUAGE_STANDARD = c++20
CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = NO
CONFIGURATION_BUILD_DIR = ${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-menu
FRAMEWORK_SEARCH_PATHS = $(inherited) "${PODS_CONFIGURATION_BUILD_DIR}/DoubleConversion" "${PODS_CONFIGURATION_BUILD_DIR}/EXManifests" "${PODS_CONFIGURATION_BUILD_DIR}/ExpoModulesCore" "${PODS_CONFIGURATION_BUILD_DIR}/RCT-Folly" "${PODS_CONFIGURATION_BUILD_DIR}/RCTDeprecation" "${PODS_CONFIGURATION_BUILD_DIR}/RCTTypeSafety" "${PODS_CONFIGURATION_BUILD_DIR}/React-Core" "${PODS_CONFIGURATION_BUILD_DIR}/React-CoreModules" "${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricComponents" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage" "${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager" "${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer" "${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAnimation" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTAppDelegate" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTBlob" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFBReactNativeSpec" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTImage" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTNetwork" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTRuntime" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTText" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeHermes" "${PODS_CONFIGURATION_BUILD_DIR}/React-cxxreact" "${PODS_CONFIGURATION_BUILD_DIR}/React-debug" "${PODS_CONFIGURATION_BUILD_DIR}/React-defaultsnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-domnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflagsnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-graphics" "${PODS_CONFIGURATION_BUILD_DIR}/React-hermes" "${PODS_CONFIGURATION_BUILD_DIR}/React-idlecallbacksnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsi" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsiexecutor" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling" "${PODS_CONFIGURATION_BUILD_DIR}/React-logger" "${PODS_CONFIGURATION_BUILD_DIR}/React-microtasksnativemodule" "${PODS_CONFIGURATION_BUILD_DIR}/React-oscompat" "${PODS_CONFIGURATION_BUILD_DIR}/React-perflogger" "${PODS_CONFIGURATION_BUILD_DIR}/React-performancetimeline" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererconsistency" "${PODS_CONFIGURATION_BUILD_DIR}/React-renderercss" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug" "${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler" "${PODS_CONFIGURATION_BUILD_DIR}/React-utils" "${PODS_CONFIGURATION_BUILD_DIR}/ReactAppDependencyProvider" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCodegen" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon" "${PODS_CONFIGURATION_BUILD_DIR}/SocketRocket" "${PODS_CONFIGURATION_BUILD_DIR}/Yoga" "${PODS_CONFIGURATION_BUILD_DIR}/expo-dev-menu-interface" "${PODS_CONFIGURATION_BUILD_DIR}/fmt" "${PODS_CONFIGURATION_BUILD_DIR}/glog" "${PODS_ROOT}/hermes-engine/destroot/Library/Frameworks/universal" "${PODS_XCFRAMEWORKS_BUILD_DIR}/hermes-engine/Pre-built"
GCC_PREPROCESSOR_DEFINITIONS = $(inherited) COCOAPODS=1 EX_DEV_MENU_ENABLED=1
HEADER_SEARCH_PATHS = $(inherited) "${PODS_ROOT}/Headers/Public" "${PODS_ROOT}/Headers/Public/FBLazyVector" "${PODS_ROOT}/Headers/Public/RCTRequired" "${PODS_ROOT}/Headers/Public/React-callinvoker" "${PODS_ROOT}/Headers/Public/React-runtimeexecutor" "${PODS_ROOT}/Headers/Public/React-timing" "${PODS_ROOT}/Headers/Public/boost" "${PODS_ROOT}/Headers/Public/fast_float" "${PODS_ROOT}/Headers/Public/hermes-engine" "${PODS_ROOT}/Headers/Private/React-Core" "$(PODS_CONFIGURATION_BUILD_DIR)/ExpoModulesCore/Swift Compatibility Header" "$(PODS_CONFIGURATION_BUILD_DIR)/expo-dev-menu-interface/Swift Compatibility Header" "${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer/React_Mapbuffer.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple/React_RuntimeApple.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore/React_RuntimeCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler/React_jserrorhandler.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing/jsinspector_moderntracing.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling/JSITooling.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-nativeconfig/React_nativeconfig.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler/React_runtimescheduler.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-performancetimeline/React_performancetimeline.framework/Headers" "$(PODS_ROOT)/boost" "$(PODS_ROOT)/Headers/Private/Yoga" "$(PODS_ROOT)/DoubleConversion" "$(PODS_ROOT)/fast_float/include" "$(PODS_ROOT)/fmt/include" "${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios" "${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage/React_FabricImage.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core" "${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric/RCTFabric.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-utils/React_utils.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags/React_featureflags.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-debug/React_debug.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager/React_ImageManager.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug/React_rendererdebug.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-renderercss/React_renderercss.framework/Headers" "${PODS_ROOT}/Headers/Private/React-Core" "$(PODS_CONFIGURATION_BUILD_DIR)/ExpoModulesCore/Swift Compatibility Header" "$(PODS_CONFIGURATION_BUILD_DIR)/expo-dev-menu-interface/Swift Compatibility Header" "${PODS_CONFIGURATION_BUILD_DIR}/React-Mapbuffer/React_Mapbuffer.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeApple/React_RuntimeApple.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RuntimeCore/React_RuntimeCore.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-jserrorhandler/React_jserrorhandler.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspectortracing/jsinspector_moderntracing.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsitooling/JSITooling.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-nativeconfig/React_nativeconfig.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-runtimescheduler/React_runtimescheduler.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-performancetimeline/React_performancetimeline.framework/Headers" "$(PODS_ROOT)/boost" "$(PODS_ROOT)/Headers/Private/Yoga" "$(PODS_ROOT)/DoubleConversion" "$(PODS_ROOT)/fast_float/include" "$(PODS_ROOT)/fmt/include" "${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-graphics/React_graphics.framework/Headers/react/renderer/graphics/platform/ios" "${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-Fabric/React_Fabric.framework/Headers/react/renderer/components/view/platform/cxx" "${PODS_CONFIGURATION_BUILD_DIR}/React-FabricImage/React_FabricImage.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/ReactCommon/ReactCommon.framework/Headers/react/nativemodule/core" "${PODS_CONFIGURATION_BUILD_DIR}/React-NativeModulesApple/React_NativeModulesApple.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-RCTFabric/RCTFabric.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-utils/React_utils.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-featureflags/React_featureflags.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-debug/React_debug.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-ImageManager/React_ImageManager.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererdebug/React_rendererdebug.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-renderercss/React_renderercss.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-rendererconsistency/React_rendererconsistency.framework/Headers" "${PODS_CONFIGURATION_BUILD_DIR}/React-jsinspector/jsinspector_modern.framework/Headers"
OTHER_CPLUSPLUSFLAGS = $(inherited) -DRCT_NEW_ARCH_ENABLED=1 -DFOLLY_NO_CONFIG -DFOLLY_MOBILE=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -Wno-comma -Wno-shorten-64-to-32
OTHER_SWIFT_FLAGS = $(inherited) -D COCOAPODS -DEX_DEV_MENU_ENABLED $(inherited) -DRCT_NEW_ARCH_ENABLED
PODS_BUILD_DIR = ${BUILD_DIR}
PODS_CONFIGURATION_BUILD_DIR = ${PODS_BUILD_DIR}/$(CONFIGURATION)$(EFFECTIVE_PLATFORM_NAME)
PODS_DEVELOPMENT_LANGUAGE = ${DEVELOPMENT_LANGUAGE}
PODS_ROOT = ${SRCROOT}
PODS_TARGET_SRCROOT = ${PODS_ROOT}/../../node_modules/expo-dev-menu
PODS_XCFRAMEWORKS_BUILD_DIR = $(PODS_CONFIGURATION_BUILD_DIR)/XCFrameworkIntermediates
PRODUCT_BUNDLE_IDENTIFIER = org.cocoapods.${PRODUCT_NAME:rfc1034identifier}
SKIP_INSTALL = YES
SWIFT_COMPILATION_MODE = wholemodule
USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES = YES
